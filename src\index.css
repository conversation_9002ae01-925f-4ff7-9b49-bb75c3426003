@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=JetBrains+Mono:wght@400;500&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here.
All colors MUST be HSL.
Modern AJC Skill Hub Design System - Professional & Engaging
*/

@layer base {
  :root {
    /* Modern Professional Color Palette */
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    /* Primary: Deep Professional Blue */
    --primary: 221 83% 53%;
    --primary-foreground: 0 0% 98%;

    /* Secondary: Soft Gray */
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;

    /* Accent: Vibrant Orange */
    --accent: 24 95% 53%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221 83% 53%;

    --radius: 0.75rem;

    /* AJC Brand Colors - Enhanced */
    --ajc-primary: 221 83% 53%;
    --ajc-primary-light: 221 83% 63%;
    --ajc-primary-dark: 221 83% 43%;
    --ajc-accent: 24 95% 53%;
    --ajc-accent-light: 24 95% 63%;
    --ajc-accent-dark: 24 95% 43%;
    --ajc-success: 142 76% 36%;
    --ajc-warning: 38 92% 50%;
    --ajc-error: 0 84% 60%;

    /* Gradients */
    --ajc-gradient-primary: linear-gradient(135deg, hsl(221 83% 53%), hsl(221 83% 63%));
    --ajc-gradient-accent: linear-gradient(135deg, hsl(24 95% 53%), hsl(24 95% 63%));
    --ajc-gradient-hero: linear-gradient(135deg, hsl(221 83% 53%) 0%, hsl(24 95% 53%) 100%);
    --ajc-gradient-card: linear-gradient(145deg, hsl(0 0% 100%) 0%, hsl(210 40% 98%) 100%);

    /* Enhanced Shadows */
    --shadow-xs: 0 1px 2px 0 hsl(0 0% 0% / 0.05);
    --shadow-sm: 0 1px 3px 0 hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow-md: 0 4px 6px -1px hsl(0 0% 0% / 0.1), 0 2px 4px -2px hsl(0 0% 0% / 0.1);
    --shadow-lg: 0 10px 15px -3px hsl(0 0% 0% / 0.1), 0 4px 6px -4px hsl(0 0% 0% / 0.1);
    --shadow-xl: 0 20px 25px -5px hsl(0 0% 0% / 0.1), 0 8px 10px -6px hsl(0 0% 0% / 0.1);
    --shadow-glow: 0 0 20px hsl(221 83% 53% / 0.3);
    --shadow-glow-accent: 0 0 20px hsl(24 95% 53% / 0.3);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Dark Mode - Modern & Professional */
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    /* Primary: Bright Blue for dark mode */
    --primary: 221 83% 63%;
    --primary-foreground: 240 10% 3.9%;

    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    /* Accent: Bright Orange for dark mode */
    --accent: 24 95% 63%;
    --accent-foreground: 240 10% 3.9%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 221 83% 63%;

    /* Dark Mode Brand Colors */
    --ajc-primary: 221 83% 63%;
    --ajc-primary-light: 221 83% 73%;
    --ajc-primary-dark: 221 83% 53%;
    --ajc-accent: 24 95% 63%;
    --ajc-accent-light: 24 95% 73%;
    --ajc-accent-dark: 24 95% 53%;

    /* Dark Mode Gradients */
    --ajc-gradient-primary: linear-gradient(135deg, hsl(221 83% 63%), hsl(221 83% 73%));
    --ajc-gradient-accent: linear-gradient(135deg, hsl(24 95% 63%), hsl(24 95% 73%));
    --ajc-gradient-hero: linear-gradient(135deg, hsl(221 83% 63%) 0%, hsl(24 95% 63%) 100%);
    --ajc-gradient-card: linear-gradient(145deg, hsl(240 10% 3.9%) 0%, hsl(240 3.7% 15.9%) 100%);

    /* Dark Mode Shadows */
    --shadow-xs: 0 1px 2px 0 hsl(0 0% 0% / 0.3);
    --shadow-sm: 0 1px 3px 0 hsl(0 0% 0% / 0.4), 0 1px 2px -1px hsl(0 0% 0% / 0.4);
    --shadow-md: 0 4px 6px -1px hsl(0 0% 0% / 0.4), 0 2px 4px -2px hsl(0 0% 0% / 0.4);
    --shadow-lg: 0 10px 15px -3px hsl(0 0% 0% / 0.4), 0 4px 6px -4px hsl(0 0% 0% / 0.4);
    --shadow-xl: 0 20px 25px -5px hsl(0 0% 0% / 0.4), 0 8px 10px -6px hsl(0 0% 0% / 0.4);
    --shadow-glow: 0 0 20px hsl(221 83% 63% / 0.4);
    --shadow-glow-accent: 0 0 20px hsl(24 95% 63% / 0.4);

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 221 83% 63%;
    --sidebar-primary-foreground: 240 10% 3.9%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 221 83% 63%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Typography Scale */
  h1, h2, h3, h4, h5, h6 {
    @apply font-heading font-semibold tracking-tight;
  }

  h1 {
    @apply text-4xl lg:text-5xl xl:text-6xl;
    line-height: 1.1;
  }

  h2 {
    @apply text-3xl lg:text-4xl;
    line-height: 1.2;
  }

  h3 {
    @apply text-2xl lg:text-3xl;
    line-height: 1.3;
  }

  h4 {
    @apply text-xl lg:text-2xl;
    line-height: 1.4;
  }

  h5 {
    @apply text-lg lg:text-xl;
    line-height: 1.5;
  }

  h6 {
    @apply text-base lg:text-lg;
    line-height: 1.5;
  }

  p {
    @apply leading-7;
  }

  /* Smooth transitions for all interactive elements */
  button, a, input, textarea, select {
    @apply transition-all duration-200 ease-in-out;
  }

  /* Focus styles */
  button:focus-visible,
  a:focus-visible,
  input:focus-visible,
  textarea:focus-visible,
  select:focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }
}

@layer components {
  /* Modern Card Styles */
  .card-modern {
    @apply bg-card border border-border rounded-xl shadow-soft backdrop-blur-sm;
    background: var(--ajc-gradient-card);
  }

  .card-hover {
    @apply transition-all duration-300 ease-out hover:shadow-medium hover:-translate-y-2;
  }

  .card-hover:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }

  .card-interactive {
    @apply transition-all duration-300 ease-out cursor-pointer;
  }

  .card-interactive:hover {
    transform: translateY(-4px) rotateY(2deg);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
  }

  .card-glow {
    @apply shadow-glow;
  }

  /* Button Variants */
  .btn-gradient {
    background: var(--ajc-gradient-primary);
    background-size: 200% 200%;
    @apply text-primary-foreground border-0 shadow-md hover:shadow-lg transition-all duration-300;
  }

  .btn-gradient:hover {
    background: var(--ajc-gradient-primary);
    background-size: 200% 200%;
    animation: gradient-shift 2s ease infinite;
    filter: brightness(1.1);
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.4);
  }

  .btn-gradient:active {
    transform: translateY(-1px) scale(1.01);
  }

  .btn-accent {
    background: var(--ajc-gradient-accent);
    background-size: 200% 200%;
    @apply text-accent-foreground border-0 shadow-md hover:shadow-lg transition-all duration-300;
  }

  .btn-accent:hover {
    background: var(--ajc-gradient-accent);
    background-size: 200% 200%;
    animation: gradient-shift 2s ease infinite;
    filter: brightness(1.1);
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 10px 25px rgba(251, 146, 60, 0.4);
  }

  .btn-accent:active {
    transform: translateY(-1px) scale(1.01);
  }

  /* Animation Classes */
  .animate-fade-in {
    animation: fade-in 0.5s ease-out;
  }

  .animate-slide-up {
    animation: fade-in-up 0.6s ease-out;
  }

  .animate-scale-in {
    animation: scale-in 0.3s ease-out;
  }

  .animate-bounce-in {
    animation: bounce-in 0.6s ease-out;
  }

  /* Shimmer Effect */
  .shimmer {
    position: relative;
    overflow: hidden;
  }

  .shimmer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    animation: shimmer 2s infinite;
  }

  /* Glass Effect */
  .glass {
    @apply backdrop-blur-md bg-white/10 border border-white/20;
  }

  .glass-dark {
    @apply backdrop-blur-md bg-black/10 border border-white/10;
  }

  /* Skeleton Loading */
  .skeleton {
    @apply bg-muted rounded animate-skeleton;
  }

  .skeleton-text {
    @apply h-4 bg-muted rounded animate-skeleton;
  }

  .skeleton-avatar {
    @apply w-10 h-10 bg-muted rounded-full animate-skeleton;
  }

  /* Enhanced Hover Effects */
  .hover-lift {
    @apply transition-all duration-300 hover:-translate-y-1 hover:shadow-lg;
  }

  .hover-glow {
    @apply transition-all duration-300;
  }

  .hover-glow:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
  }

  .hover-scale {
    @apply transition-transform duration-300 hover:scale-105;
  }

  /* Floating Elements */
  .float-animation {
    animation: float 6s ease-in-out infinite;
  }

  .float-delayed {
    animation: float 6s ease-in-out infinite;
    animation-delay: 2s;
  }
}